import json
import os
from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1

from blitzy_utils.consts import REPO_METADATA_NAME, CODE_ONBOARDING_BATCH_SIZE
from blitzy_utils.common import publish_notification, \
    setup_maintenance_signal_handlers, get_repo_blitzy_folder_path, get_existing_product_files_list_name, \
    get_existing_product_updated_files_list_name, get_batched_files_list_filename, \
    upload_text_to_gcs_using_admin_service, download_text_file_from_gcs_using_admin_service
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_utils.github import get_head_commit_hash, get_changed_files_between_commits, download_repository_to_disk

from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.common.utils import archie_exponential_retry

PROJECT_ID = os.environ["PROJECT_ID"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
GRAPH_CODE_TOPIC = os.environ["GRAPH_CODE_TOPIC"]
EVENT_DATA = os.environ["EVENT_DATA"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
PRIVATE_BLOB_NAME = os.environ["PRIVATE_BLOB_NAME"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]

BATCH_SIZE = CODE_ONBOARDING_BATCH_SIZE

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def process_event(event_data_str: str):
    setup_maintenance_signal_handlers()

    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    team_id = event_data.get('team_id', 'default')
    company_id = event_data.get('company_id', 'default')
    org_name = event_data.get('org_name', '')
    repo_id = event_data.get('repo_id', '')
    branch_name = event_data.get('branch_name', 'main')
    branch_id = event_data.get('branch_id', branch_name)
    head_commit_hash = event_data.get('head_commit_hash', "")
    prev_head_commit_hash = event_data.get('prev_head_commit_hash', "")
    tech_spec_id = event_data.get('tech_spec_id', "")
    git_project_repo_id = event_data.get('git_project_repo_id', '')

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "tech_spec_id": tech_spec_id,
        "org_name": org_name,
        "repo_id": repo_id,
        "branch_name": branch_name,
        "phase": ProjectPhase.CODE_DOWNLOAD.value,
        "status": JobStatus.IN_PROGRESS.value,
        "user_id": user_id,
        "git_project_repo_id": git_project_repo_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    if not head_commit_hash:
        head_commit_hash = get_head_commit_hash(
            repo_name=repo_name,
            user_id=user_id,
            server=GITHUB_SECRET_SERVER,
            branch_name=branch_name,
            repo_id=repo_id,
            git_project_repo_id=git_project_repo_id,
        )

    blitzy_folder_path = get_repo_blitzy_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=PRIVATE_BLOB_NAME
    )

    graph_needs_update, repo_metadata, no_previous_metadata = process_repo_metadata(
        repo_name=repo_name,
        repo_id=repo_id,
        branch_name=branch_name,
        branch_id=branch_id,
        blitzy_folder_path=blitzy_folder_path,
        head_commit_hash=head_commit_hash,
        company_id=company_id
    )
    if no_previous_metadata:
        prev_head_commit_hash = ""
    elif not prev_head_commit_hash and repo_metadata["prev_head_commit_hash"]:
        prev_head_commit_hash = repo_metadata["prev_head_commit_hash"]
        logger.info(f'overriding prev_head_commit_hash to {prev_head_commit_hash}')

    if not graph_needs_update:
        logger.info(f'Graph does not need to be updated for repo {repo_name}')
    else:
        batch_indexes, total_batches, total_files = download_and_upload(
            repo_name=repo_name,
            branch_name=branch_name,
            user_id=user_id,
            blitzy_folder_path=blitzy_folder_path,
            head_commit_hash=head_commit_hash,
            prev_head_commit_hash=prev_head_commit_hash,
            company_id=company_id,
            repo_id=repo_id,
            git_project_repo_id=git_project_repo_id
        )
        if not total_files:
            logger.info('Skipping Graph update as no files found')
            graph_needs_update = False
        else:
            if propagate:
                for batch_index in batch_indexes:
                    notification_data = {
                        'repo_name': repo_name,
                        'repo_id': repo_id,
                        'branch_id': branch_id,
                        'branch_name': branch_name,
                        'company_id': company_id,
                        'user_id': user_id,
                        'team_id': team_id,
                        'job_id': job_id,
                        'project_id': project_id,
                        'head_commit_hash': head_commit_hash,
                        'prev_head_commit_hash': prev_head_commit_hash,
                        'propagate': propagate,
                        'batch_index': batch_index,
                        'total_batches': total_batches,
                        'tech_spec_id': tech_spec_id,
                        'git_project_repo_id': git_project_repo_id,
                        'resume': False
                    }
                    publish_notification(
                        publisher=publisher,
                        notification_data=notification_data,
                        project_id=PROJECT_ID,
                        topic_id=GRAPH_CODE_TOPIC
                    )
    logger.info(f'Sending platform event')
    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "tech_spec_id": tech_spec_id,
        "org_name": org_name,
        "repo_id": repo_id,
        "branch_name": branch_name,
        "branch_id": branch_id,
        "head_commit_hash": head_commit_hash,
        "prev_head_commit_hash": prev_head_commit_hash,
        "phase": ProjectPhase.CODE_DOWNLOAD.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "team_id": team_id,
        "company_id": company_id,
        "graph_needs_update": graph_needs_update,
        "repo_name": repo_name,
        "git_project_repo_id": git_project_repo_id,
        "metadata": {
            "propagate": propagate,
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
    logger.info(f'Done sending platform event')


def process_repo_metadata(
        repo_name: str,
        repo_id: str,
        branch_name: str,
        branch_id: str,
        blitzy_folder_path: str,
        head_commit_hash: str,
        company_id: str,
):
    no_previous_metadata = False
    repo_metadata_filename = f"{REPO_METADATA_NAME}.json"
    repo_metadata = {}
    try:
        download_file_path = f"{blitzy_folder_path}/{repo_metadata_filename}"
        repo_metadata = json.loads(download_text_file_from_gcs_using_admin_service(
            file_path=download_file_path,
            company_id=company_id,
        ))
        logger.info(f'Repo metadata: {repo_metadata}')
        if repo_metadata["head_commit_hash"] == head_commit_hash:
            logger.info(f"Head commit hash {head_commit_hash} was seen before and hasn't changed")
            return False, repo_metadata, no_previous_metadata
        else:
            repo_metadata["prev_head_commit_hash"] = repo_metadata["head_commit_hash"]
            repo_metadata["head_commit_hash"] = head_commit_hash
            return True, repo_metadata, no_previous_metadata
    except Exception as e:
        no_previous_metadata = True
        logger.warning(f'Repo metadata file not found, treating as new repo')
        repo_metadata = {
            "repo_name": repo_name,
            "repo_id": repo_id,
            "branch_name": branch_name,
            "branch_id": branch_id,
            # init as blank
            "prev_head_commit_hash": "",
            "head_commit_hash": ""
        }

        repo_metadata_str = json.dumps(repo_metadata)
        destination_blob_name = f"{blitzy_folder_path}/{repo_metadata_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_blob_name,
            company_id=company_id,
            data=repo_metadata_str,
        )

    return True, repo_metadata, no_previous_metadata


@archie_exponential_retry()
def download_and_upload(
    repo_name: str,
    branch_name: str,
    user_id: str,
    blitzy_folder_path: str,
    head_commit_hash: str,
    prev_head_commit_hash: str,
    company_id: str,
    repo_id: str,
    git_project_repo_id: str
):
    logger.info(f"Downloading {repo_name}")

    github_file_paths = []
    files_list_filename_base = ""
    if prev_head_commit_hash:
        logger.info(f"Getting changed file paths from repository: {repo_name}")
        github_file_paths = get_changed_files_between_commits(
            repo_name=repo_name,
            user_id=user_id,
            server=GITHUB_SECRET_SERVER,
            base_commit=prev_head_commit_hash,
            head_commit=head_commit_hash,
            repo_id=repo_id,
            git_project_repo_id=git_project_repo_id,
        )
        files_list_filename_base = get_existing_product_updated_files_list_name(
            prev_head_commit_hash=prev_head_commit_hash,
            head_commit_hash=head_commit_hash
        )
        logger.info(f"Found {len(github_file_paths)} changed files")
    else:
        # Use git clone to download repository
        git_files = download_repository_to_disk(
            repo_name=repo_name,
            user_id=user_id,
            server=GITHUB_SECRET_SERVER,
            branch_name=branch_name,
            commit_hash=head_commit_hash,
            repo_id=repo_id,
            git_project_repo_id=git_project_repo_id,
        )

        github_file_paths = [git_file.path for git_file in git_files]

        files_list_filename_base = get_existing_product_files_list_name(
            head_commit_hash=head_commit_hash
        )
        logger.info(f"Downloaded {len(github_file_paths)} files from repository")

    file_paths = []
    skipped_count = 0
    for idx, github_path in enumerate(github_file_paths):
        logger.info(f"Processing file {idx + 1}/{len(github_file_paths)}: {github_path}")

        # TODO solidify this logic
        if is_source_adjacent_file(github_path) or is_source_file(github_path) and \
                not should_exclude_path(github_path):
            file_paths.append(github_path)
        else:
            skipped_count += 1
            logger.warning(f'Skipping file {skipped_count}: {github_path}')

    total_files = len(file_paths)
    logger.info(f"Total files to process: {total_files} (skipped {skipped_count})")

    # Calculate total batches - ceiling division to ensure all files are included
    total_batches = (total_files + BATCH_SIZE - 1) // BATCH_SIZE

    batch_indexes = list(range(total_batches))

    logger.info(f"Created {total_batches} batches of file paths (batch size: {BATCH_SIZE})")

    # Upload each batch with its index appended to the filename
    for batch_index in batch_indexes:
        start_idx = batch_index * BATCH_SIZE
        end_idx = min(start_idx + BATCH_SIZE, total_files)

        batch_files = file_paths[start_idx:end_idx]

        files_list_filename = f"{get_batched_files_list_filename(base_filename=files_list_filename_base, batch_index=batch_index)}.json"
        logger.info(f"Uploading batch {batch_index + 1}/{total_batches} with {len(batch_files)} files")

        destination_blob_name = f"{blitzy_folder_path}/{files_list_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_blob_name,
            company_id=company_id,
            data=json.dumps(batch_files)
        )

        logger.info(f"Successfully uploaded batch {batch_index + 1}")
    # Now trigger the notification for each batch
    return batch_indexes, total_batches, total_files


def should_exclude_path(path):
    """Check if path should be excluded (contains dependency directories)"""
    exclude_dirs = ['node_modules', 'yarn_cache', '.yarn/cache']
    return any(exclude_dir in path for exclude_dir in exclude_dirs)


if __name__ == "__main__":
    logger.info(f"Processing notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    org_name = event_data.get('org_name', '')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_name = event_data.get('branch_name', 'main')
    tech_spec_id = event_data.get('tech_spec_id', "")
    git_project_repo_id = event_data.get('git_project_repo_id', '')

    try:
        process_event(event_data_str=EVENT_DATA)
    except Exception as e:
        logger.error(f'Failed to download code: {e}')
        notification_data = {
            "projectId": project_id,
            "jobId": job_id,
            "tech_spec_id": tech_spec_id,
            "org_name": org_name,
            "repo_id": repo_id,
            "branch_name": branch_name,
            "phase": ProjectPhase.CODE_DOWNLOAD.value,
            "status": JobStatus.FAILED.value,
            "user_id": user_id,
            "repo_name": repo_name,
            "git_project_repo_id": git_project_repo_id,
            "metadata": {
                "propagate": propagate,
            }
        }
        publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
        raise
