from ssl import <PERSON><PERSON>rror

from blitzy_utils.logger import logger
from google.api_core import exceptions
from google.cloud import storage
from google.cloud.exceptions import Conflict, Forbidden, NotFound
from requests import exceptions as request_exceptions
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_fixed

from src.consts import GCS_BUCKET_NAME, storage_client
from src.error.errors import ConflictingResourceError

# Constants for retry mechanism
DEFAULT_MAX_RETRIES = 5
DEFAULT_SLEEP_TIME = 30


@retry(
    retry=retry_if_exception_type(
        (
            exceptions.ServerError,
            exceptions.TooManyRequests,
            request_exceptions.Timeout,
            request_exceptions.ConnectionError,
            SSLError,
            TimeoutError
        )
    ),
    stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
    wait=wait_fixed(DEFAULT_SLEEP_TIME)
)
def copy_gcs_file(source_path, destination_path):
    """
    Copies a file from one path to another in Google Cloud Storage (GCS).

    This function includes retry logic for handling rate limiting and transient errors,
    and checks for file existence before attempting to copy.

    :param source_path: The path of the source file in the bucket.
    :type source_path: str

    :param destination_path: The path where the file will be copied to in
        the bucket.
    :type destination_path: str

    :return: Returns True upon successful copy operation.
    :rtype: bool

    :raises NotFound: If the source file does not exist.
    :raises Exception: For other GCS operation errors.
    """
    bucket = storage_client.bucket(GCS_BUCKET_NAME)
    source_blob = bucket.blob(source_path)

    # Check if source file exists before attempting to copy
    if not source_blob.exists():
        logger.warning(f"Source file does not exist: {source_path}")
        raise NotFound(f"Source file not found: {source_path}")

    # Perform the copy operation
    bucket.copy_blob(source_blob, bucket, destination_path)

    logger.info(f"File {source_path} copied to {destination_path} in bucket {GCS_BUCKET_NAME}.")
    return True


def copy_gcs_file_if_exists(source_path, destination_path):
    """
    Copies a file from one path to another in Google Cloud Storage (GCS) if the source exists.

    This is a safe wrapper around copy_gcs_file that doesn't raise an exception
    if the source file doesn't exist.

    :param source_path: The path of the source file in the bucket.
    :type source_path: str

    :param destination_path: The path where the file will be copied to in
        the bucket.
    :type destination_path: str

    :return: Returns True if file was copied, False if source doesn't exist.
    :rtype: bool
    """
    try:
        return copy_gcs_file(source_path, destination_path)
    except NotFound:
        logger.info(f"Source file does not exist, skipping copy: {source_path}")
        return False
    except Exception as e:
        logger.error(f"Error copying file from {source_path} to {destination_path}: {str(e)}")
        raise


def create_bucket(bucket_name, location='US'):
    client = storage.Client()

    try:
        # Create the bucket
        bucket = client.bucket(bucket_name)
        bucket.location = location
        bucket = client.create_bucket(bucket)

        logger.info(f"Bucket {bucket.name} created successfully in {bucket.location}")
        return bucket

    except Conflict:
        logger.warning(f"Bucket {bucket_name} already exists")
        raise ConflictingResourceError(f"Bucket {bucket_name} already exists")
    except Exception as e:
        logger.error(f"Error creating bucket: {e}")
        return None


def bucket_exists(bucket_name):
    client = storage.Client()

    try:
        client.get_bucket(bucket_name)
        return True
    except NotFound:
        return False
    except Forbidden:
        logger.warning(f"Access denied to bucket {bucket_name}. It may exist but you don't have permission.")
        raise ConflictingResourceError(
            f"Access denied to bucket {bucket_name}. It may exist but you don't have permission.")
    except Exception as e:
        logger.error(f"Error checking bucket: {e}")
        raise e
