import base64
import hashlib
import hmac
import json
import os
import time
from urllib.parse import urlencode, urljoin

from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from flask import Blueprint, redirect, request
from flask_utils.decorators import flask_pydantic_response

from src.api.models import GetAzureAuthURL
from src.consts import JWT_AUDIENCE, PLATFORM_URL
from src.error.errors import (AzureAlreadyIntegrated, EndpointMisconfigured,
                              Status400Error)
from src.middleware.decorators import get_user_info

SCOPE = "499b84ac-1321-427f-aa17-267ca6975798/.default offline_access"

azure_bp = Blueprint("azure_bp", __name__, url_prefix="/v1/azure")


@azure_bp.route("/auth_url", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_auth_url(user_info: dict):
    """
    Generate Azure OAuth2 authorization URL using environment variables.
    Returns a 500 error if required environment variables are missing.
    """

    # Required environment variables for Azure OAuth
    client_id = os.environ.get("AZURE_CLIENT_ID")
    redirect_uri = os.environ.get("AZURE_REDIRECT_URI")

    # Check for missing required environment variables
    missing_vars = []
    if not client_id:
        missing_vars.append("AZURE_CLIENT_ID")
    if not redirect_uri:
        missing_vars.append("AZURE_REDIRECT_URI")

    if missing_vars:
        error_message = (
            f"Missing required environment variables: {', '.join(missing_vars)}"
        )
        raise EndpointMisconfigured(error_message)

    # Build the authorization URL`
    base_url = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize"

    # OAuth2 parameters matching your URL structure
    params = {
        "client_id": client_id,
        "response_type": "code",
        "redirect_uri": urljoin(JWT_AUDIENCE, redirect_uri),
        "response_mode": "query",
        # Scope configured in the entra app. UUID is for azure devops, offline access
        # required to get a refresh token
        "scope": SCOPE,
        "state": generate_state(user_info["id"]),
    }

    # Construct the full authorization URL
    auth_url = f"{base_url}?{urlencode(params)}"
    logger.debug(f"Generated Azure OAuth2 authorization URL: {auth_url}")

    return GetAzureAuthURL(authUrl=auth_url), 200


@azure_bp.route("/auth/success", methods=["GET"])
@flask_pydantic_response
def get_auth_success():
    # Get parameters from a query string (no
    auth_code = request.args.get("code")
    state = request.args.get("state")

    # Validate required parameters
    if not auth_code:
        raise Status400Error("Missing 'code' parameter")

    if not state:
        raise Status400Error("Missing 'state' parameter")

    # Verify and decode the state parameter
    try:
        state_payload = decode_state(state)
        user_id = state_payload.get("user_id")
    except ValueError as exc:
        logger.warning(f"State field in azure callback is incorrect or invalid: {exc}")
        raise Status400Error("Invalid state in Azure cloud callback") from exc

    logger.info(f"User {user_id} authenticated successfully in Azure cloud")

    azure_success_redirect_uri = os.environ.get("AZURE_SUCCESS_AUTH_REDIRECT_URI")
    # Check for missing required environment variables
    if not azure_success_redirect_uri:
        raise EndpointMisconfigured(
            "Missing required environment variables: AZURE_SUCCESS_AUTH_REDIRECT_URI"
        )

    redirect_uri = urljoin(
        PLATFORM_URL,
        azure_success_redirect_uri,
    )

    git_client_install_azure(auth_code, user_id)

    logger.info(f"Redirecting user to {redirect_uri}")
    return redirect(redirect_uri)


def git_client_install_azure(code: str, user_id: str):
    """
    Calls github service handler to install azure devops
    Args:
        code: oauth code from azure to perform exchange
        user_id: id of the user
    Returns:
    """

    payload = {
        "code": code,
        "user_id": user_id,
        "redirect_uri": urljoin(
            JWT_AUDIENCE, os.environ.get("AZURE_REDIRECT_URI")
        ),  # needed when we will use a refresh token to get access token
        "requested_scope": SCOPE,  # needed when we will use a refresh token to get access
    }

    with ServiceClient() as client:
        response = client.post("github", "/v1/azure/install", json=payload)
        try:
            response_output = response.json()
            if response.status_code == 409:
                logger.info(f"Azure client already installed for this organization. Response: {response.text}")
                raise AzureAlreadyIntegrated(
                    "Azure client already installed for this organization. Please request access from your admin"
                )
            elif response.status_code > 299:
                raise ValueError(f"Failed to install Azure client: {response_output}")
        except ValueError as e:
            logger.error(f"Failed to parse JSON response: {response.text}, error: {e}")
            raise
        return None


def generate_state(user_id: str) -> str:
    """
    Generate a stateless, cryptographically secure state parameter using HMAC.
    The state contains user info and timestamp, signed with a secret key.
    """
    secret_key = os.environ.get("AZURE_STATE_SECRET")
    if not secret_key:
        raise EndpointMisconfigured(
            "Missing required environment variables: AZURE_STATE_SECRET"
        )

    payload = {
        "user_id": user_id,
        "timestamp": int(time.time()),
        "exp": int(time.time()) + 1200,  # Expires in 20 minutes, time chosen randomly
    }

    logger.debug(f"Generated state payload: {payload}")

    # Convert to JSON and encode
    payload_json = json.dumps(payload, separators=(",", ":"))  # Compact JSON
    payload_b64 = base64.urlsafe_b64encode(payload_json.encode()).decode().rstrip("=")

    # Create HMAC signature
    signature = hmac.new(
        secret_key.encode(), payload_b64.encode(), hashlib.sha256
    ).hexdigest()

    # Combine payload and signature
    state = f"{payload_b64}.{signature}"

    return state


def decode_state(state: str) -> dict:
    """
    Decode a stateless state parameter.
    Returns the payload if valid, raises an exception if invalid/expired.
    """
    secret_key = os.environ.get("AZURE_STATE_SECRET")
    if not secret_key:
        raise EndpointMisconfigured(
            "Missing required environment variables: AZURE_STATE_SECRET"
        )
    try:
        # Split payload and signature
        payload_b64, received_signature = state.split(".", 1)

        # Verify signature
        expected_signature = hmac.new(
            secret_key.encode(), payload_b64.encode(), hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(received_signature, expected_signature):
            raise ValueError("Invalid state signature")

        # Decode payload
        payload_b64 += "=" * (4 - len(payload_b64) % 4)
        payload_json = base64.urlsafe_b64decode(payload_b64).decode()
        payload = json.loads(payload_json)

        logger.debug(f"Decoded state payload: {payload}")

        # Check expiration
        if int(time.time()) > payload.get("exp", 0):
            raise ValueError("State has expired")

        return payload

    except (ValueError, json.JSONDecodeError, KeyError) as e:
        raise ValueError(f"Invalid state parameter: {str(e)}")


# Azure DevOps endpoints - implemented in archie-github-handler service
# These endpoints call the archie-github-handler service which implements the Azure DevOps API integration
# with project-based structure to properly handle Azure DevOps organizational hierarchy


@azure_bp.route("/accounts", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_azure_accounts(user_info):
    """
    Get Azure DevOps organizations for the authenticated user.

    This endpoint only handles Azure DevOps organizations.
    """
    user_id = user_info["id"]
    response = get_organizations_from_azure_handler(user_id)
    return response, 200


@azure_bp.route("/accounts/<org_id>/projects", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_projects_by_azure_org_id(user_info, org_id):
    """
    Get projects from a specific Azure DevOps organization.

    This endpoint calls archie-github-handler service to get Azure DevOps projects.
    """
    user_id = user_info["id"]
    response = get_projects_from_azure_handler(user_id, org_id)
    return response, 200


@azure_bp.route("/accounts/<org_id>/projects/<project_id>/repos", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_repositories_by_azure_project_id(user_info, org_id, project_id):
    """
    Get repositories from a specific Azure DevOps project.

    This endpoint calls archie-github-handler service to get repositories from a specific project.
    """
    user_id = user_info["id"]
    response = get_repos_from_azure_handler(user_id, org_id, project_id)
    return response, 200


@azure_bp.route("/accounts/<org_id>/repos/<repo_id>/branches", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_branches_by_azure_repo_id_unified(user_info, org_id, repo_id):
    """
    Get branches from a specific Azure DevOps repository.

    This endpoint derives the project_id automatically from the database.
    """
    user_id = user_info["id"]
    response = get_branches_from_azure_handler_unified(user_id, org_id, repo_id)
    return response, 200


@azure_bp.route(
    "/accounts/<org_id>/projects/<project_id>/repos/<repo_id>/branches",
    methods=["GET"],
)
@get_user_info()
@flask_pydantic_response
def get_branches_by_azure_repo_id(user_info, org_id, project_id, repo_id):
    """
    Get branches from a specific Azure DevOps repository.

    This endpoint calls archie-github-handler service to get branches from a repository.
    """
    user_id = user_info["id"]
    response = get_branches_from_azure_handler_with_project(
        user_id, org_id, project_id, repo_id
    )
    return response, 200


def get_organizations_from_azure_handler(user_id: str):
    """
    Call archie-github-handler service to get Azure DevOps organizations for a user.
    """
    with ServiceClient() as client:
        response = client.get("github", f"/v1/azure/users/{user_id}/organizations")
        response.raise_for_status()
        return response.json()


def get_projects_from_azure_handler(user_id: str, org_id: str):
    """
    Call archie-github-handler service to get Azure DevOps projects for an organization.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects",
        )
        response.raise_for_status()
        return response.json()


def get_repos_from_azure_handler(user_id: str, org_id: str, project_id: str):
    """
    Call archie-github-handler service to get repositories from Azure DevOps project.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects/{project_id}/repositories",
        )
        response.raise_for_status()
        return response.json()


def get_branches_from_azure_handler_unified(user_id: str, org_id: str, repo_id: str):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository.
    This uses the endpoint that derives project_id automatically from the database.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/azure/users/{user_id}/organizations/{org_id}/repositories/{repo_id}/branches",
        )
        response.raise_for_status()
        return response.json()


def get_branches_from_azure_handler_with_project(
    user_id: str, org_id: str, project_id: str, repo_id: str
):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository
    with explicit project. This uses the hierarchical endpoint with explicit project_id.
    """
    with ServiceClient() as client:
        endpoint = (
            f"/v1/azure/users/{user_id}/organizations/{org_id}/projects/"
            f"{project_id}/repositories/{repo_id}/branches"
        )
        response = client.get("github", endpoint)
        response.raise_for_status()
        return response.json()
