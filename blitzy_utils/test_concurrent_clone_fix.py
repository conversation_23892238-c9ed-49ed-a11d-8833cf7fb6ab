#!/usr/bin/env python3
"""
Test script to verify the concurrent clone fix works correctly.
This test simulates the scenario where multiple processes try to clone
the same repository to the same destination path concurrently.
"""

import os
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add the blitzy_utils directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_concurrent_github_clone():
    """Test concurrent GitHub clones to the same path using real test data."""
    print("🧪 Testing Concurrent GitHub Clone Fix")
    print("=" * 50)

    # Import after path setup
    from blitzy_utils.github import download_repository_to_disk

    # Real GitHub test data
    test_data =    {"job_id": "f8e84f97-0e11-4e48-9e2c-a6112f72d8c8", "resume": True, "repo_id": "1009666501", "team_id": "default", "user_id": "f5fcdd7b-7cc0-4233-8cc0-7e2d06b2da9b", "branch_id": "54c44cf6-4872-4738-8257-1f977103fd72", "propagate": True, "repo_name": "NP-V1", "company_id": "default", "project_id": "1474cbdf-41c5-46f9-bb6f-1655270420d4", "branch_name": "main", "tech_spec_id": "a48299b4-441c-4fd4-8e73-a212ca487f9a", "head_commit_hash": "6943985cd16b0354eef0a13356cf1ff149a43322", "prev_head_commit_hash": "23bd26e84631055bd6e1571488cd197b84ae5772", "previous_tech_spec_id": "6d9f9796-7464-49aa-8446-c75d39b2ed52"}

    print(f"🔄 Testing with real GitHub repo: {test_data['repo_name']}")
    print(f"🔄 Branch: {test_data['branch_name']}")
    print(f"� Commit: {test_data['head_commit_hash']}")
    print(f"🔄 Repo ID: {test_data['repo_id']}")

    # Function to run clone in a thread
    def clone_worker(worker_id):
        print(f"🔄 Worker {worker_id} starting GitHub clone...")
        try:
            # Use the real download_repository_to_disk function
            git_files = download_repository_to_disk(
                repo_name=test_data['repo_name'],
                branch_name=test_data['branch_name'],
                user_id=test_data['user_id'],
                server="github-server",  # This will be used for token lookup
                commit_hash=test_data['head_commit_hash'],
                repo_id=test_data['repo_id']
                # Note: no git_project_repo_id means this will use GitHub implementation
            )
            print(f"✅ Worker {worker_id} completed: Downloaded {len(git_files)} files")
            return worker_id, True, None
        except Exception as e:
            print(f"❌ Worker {worker_id} failed: {e}")
            return worker_id, False, str(e)

    # Run multiple concurrent clones
    num_workers = 3
    print(f"🚀 Starting {num_workers} concurrent GitHub clone operations...")

    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(clone_worker, i) for i in range(num_workers)]

        results = []
        for future in as_completed(futures):
            worker_id, success, error = future.result()
            results.append((worker_id, success, error))

    # Analyze results
    print("\n📊 Results Summary:")
    successful_workers = [r for r in results if r[1] is True]
    failed_workers = [r for r in results if r[1] is False]

    print(f"✅ Successful workers: {len(successful_workers)}")
    print(f"❌ Failed workers: {len(failed_workers)}")

    for worker_id, success, error in failed_workers:
        print(f"   Worker {worker_id}: {error}")

    # Check if lock files were cleaned up in the blitzy directory
    import glob
    lock_files = glob.glob("blitzy/**/*.lock", recursive=True)
    if lock_files:
        print(f"⚠️  Lock files remaining: {lock_files}")
    else:
        print("✅ All lock files cleaned up properly")

    # Consider test successful if:
    # 1. At least one worker succeeded, OR
    # 2. All failures are due to authentication/network issues (expected with real repos)
    # 3. No "directory already exists" errors (the main issue we're fixing)
    directory_exists_errors = [r for r in failed_workers if r[2] and "already exists and is not an empty directory" in str(r[2])]

    if directory_exists_errors:
        print(f"❌ Found {len(directory_exists_errors)} 'directory already exists' errors - fix not working!")
        return False

    if len(successful_workers) > 0:
        print("✅ At least one worker succeeded - concurrent access is working!")
        return True

    # All failed - check if it's due to expected issues (auth, network, etc.)
    expected_failures = all(
        any(keyword in str(r[2]).lower() for keyword in ["authentication", "network", "credentials", "token", "permission"])
        for r in failed_workers if r[2]
    )

    if expected_failures:
        print("✅ All failures are due to expected authentication/network issues")
        return True

    print("❌ Unexpected failures occurred")
    return False


def test_concurrent_azure_clone():
    """Test concurrent Azure DevOps clones to the same path using real test data."""
    print("\n🧪 Testing Concurrent Azure DevOps Clone Fix")
    print("=" * 50)

    # Import after path setup
    from blitzy_utils.github import download_repository_to_disk

    # Real Azure DevOps test data
    test_data = {
        "job_id": "939a3370-387d-427d-b169-77579fd57cd9",
        "resume": True,
        "repo_id": "d9db696a-b353-40f3-9e42-07ccd5f6c947",
        "team_id": "0e930d20-17c0-4301-9a55-49b8694339d8",
        "user_id": "2887a75e-9b01-4787-a4a0-bee4e919f4f8",
        "branch_id": "df042e05-d8b4-47df-97a1-44932e0c4698",
        "propagate": True,
        "repo_name": "19July_Lakshya_back_prop",
        "company_id": "4b01c459-254e-4f28-8b5d-923584f0bf82",
        "project_id": "6877cbe0-bbfc-442c-975a-0052937b353e",
        "branch_name": "ADO_Branch_10AM",
        "tech_spec_id": "22b39eb2-657d-4e0e-8cd2-32386a514842",
        "head_commit_hash": "34fcbe813b071f8fb8e264ca54075bef38594700",
        "git_project_repo_id": "36c8d0c2-4d87-4658-920e-68d9c9754ef6",
        "prev_head_commit_hash": ""
    }
    
    print(f"🔄 Testing with real Azure DevOps repo: {test_data['repo_name']}")
    print(f"🔄 Branch: {test_data['branch_name']}")
    print(f"🔄 Commit: {test_data['head_commit_hash']}")
    print(f"🔄 Git Project Repo ID: {test_data['git_project_repo_id']}")

    # Function to run clone in a thread
    def clone_worker(worker_id):
        print(f"🔄 Worker {worker_id} starting Azure DevOps clone...")
        try:
            # Use the real download_repository_to_disk function which will detect
            # Azure DevOps based on git_project_repo_id and route to Azure implementation
            git_files = download_repository_to_disk(
                repo_name=test_data['repo_name'],
                branch_name=test_data['branch_name'],
                user_id=test_data['user_id'],
                server="azure-devops-server",  # This will be used for token lookup
                commit_hash=test_data['head_commit_hash'],
                repo_id=test_data['repo_id'],
                git_project_repo_id=test_data['git_project_repo_id']
            )
            print(f"✅ Worker {worker_id} completed: Downloaded {len(git_files)} files")
            return worker_id, True, None
        except Exception as e:
            print(f"❌ Worker {worker_id} failed: {e}")
            return worker_id, False, str(e)
        
    # Run multiple concurrent clones
    num_workers = 3
    print(f"🚀 Starting {num_workers} concurrent Azure DevOps clone operations...")

    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(clone_worker, i) for i in range(num_workers)]

        results = []
        for future in as_completed(futures):
            worker_id, success, error = future.result()
            results.append((worker_id, success, error))

    # Analyze results
    print("\n📊 Results Summary:")
    successful_workers = [r for r in results if r[1] is True]
    failed_workers = [r for r in results if r[1] is False]

    print(f"✅ Successful workers: {len(successful_workers)}")
    print(f"❌ Failed workers: {len(failed_workers)}")

    for worker_id, success, error in failed_workers:
        print(f"   Worker {worker_id}: {error}")

    # Check if lock files were cleaned up in the blitzy directory
    import glob
    lock_files = glob.glob("blitzy/**/*.lock", recursive=True)
    if lock_files:
        print(f"⚠️  Lock files remaining: {lock_files}")
    else:
        print("✅ All lock files cleaned up properly")

    # Consider test successful if:
    # 1. At least one worker succeeded, OR
    # 2. All failures are due to authentication/network issues (expected with real repos)
    # 3. No "directory already exists" errors (the main issue we're fixing)
    directory_exists_errors = [r for r in failed_workers if r[2] and "already exists and is not an empty directory" in str(r[2])]

    if directory_exists_errors:
        print(f"❌ Found {len(directory_exists_errors)} 'directory already exists' errors - fix not working!")
        return False

    if len(successful_workers) > 0:
        print("✅ At least one worker succeeded - concurrent access is working!")
        return True

    # All failed - check if it's due to expected issues (auth, network, etc.)
    expected_failures = all(
        any(keyword in str(r[2]).lower() for keyword in ["authentication", "network", "credentials", "token", "permission"])
        for r in failed_workers if r[2]
    )

    if expected_failures:
        print("✅ All failures are due to expected authentication/network issues")
        return True

    print("❌ Unexpected failures occurred")
    return False


def main():
    """Run all concurrent clone tests."""
    print("🔧 Testing Concurrent Clone Fix")
    print("=" * 70)
    
    try:
        # Test GitHub concurrent clones
        github_success = test_concurrent_github_clone()
        
        # Test Azure DevOps concurrent clones  
        azure_success = test_concurrent_azure_clone()
        
        print("\n🎯 Final Results:")
        print(f"GitHub concurrent clone test: {'✅ PASSED' if github_success else '❌ FAILED'}")
        print(f"Azure DevOps concurrent clone test: {'✅ PASSED' if azure_success else '❌ FAILED'}")
        
        if github_success and azure_success:
            print("\n🎉 All tests passed! Concurrent clone fix is working correctly.")
            return 0
        else:
            print("\n💥 Some tests failed. Please check the implementation.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
