"""
Azure DevOps Git Clone Implementation

This module provides git command line based repository cloning for Azure DevOps,
replacing API-based file downloads to improve reliability and reduce rate limits.
"""

import os
import re
import shutil
import subprocess
from typing import Optional, List
from urllib.parse import quote

from .common import blitzy_exponential_retry, BlitzyGitFile
from .logger import logger


class AzureDevOpsGitCloneError(Exception):
    """Base exception for Azure DevOps git clone operations."""
    pass


class AzureDevOpsAuthenticationError(AzureDevOpsGitCloneError):
    """Authentication failed during git clone."""
    pass


class AzureDevOpsRepositoryNotFoundError(AzureDevOpsGitCloneError):
    """Repository not found or access denied."""
    pass


class AzureDevOpsNetworkError(AzureDevOpsGitCloneError):
    """Network-related errors during git clone."""
    pass


def build_azure_devops_clone_url(organization: str, project: str, repo_name: str) -> str:
    """
    Build base Azure DevOps clone URL.
    
    Args:
        organization: Azure DevOps organization name
        project: Project name
        repo_name: Repository name
        
    Returns:
        Base clone URL without authentication
        
    Example:
        https://dev.azure.com/myorg/myproject/_git/myrepo
    """
    # URL encode components to handle special characters
    org_encoded = quote(organization, safe='')
    project_encoded = quote(project, safe='')
    repo_encoded = quote(repo_name, safe='')
    
    return f"https://dev.azure.com/{org_encoded}/{project_encoded}/_git/{repo_encoded}"


def add_auth_to_azure_devops_url(base_url: str, access_token: str) -> str:
    """
    Add authentication to Azure DevOps URL.
    
    Args:
        base_url: Base Azure DevOps clone URL
        access_token: Azure DevOps access token (PAT or OAuth)
        
    Returns:
        URL with embedded authentication
        
    Example:
        https://username:<EMAIL>/myorg/myproject/_git/myrepo
    """
    # For Azure DevOps, we can use any username with PAT
    # Common practice is to use empty string or 'oauth' for OAuth tokens
    username = "oauth" if access_token.startswith("oauth") else ""
    
    # URL encode the token to handle special characters
    token_encoded = quote(access_token, safe='')
    
    # Insert authentication into URL
    if "://" in base_url:
        protocol, rest = base_url.split("://", 1)
        if username:
            auth_url = f"{protocol}://{username}:{token_encoded}@{rest}"
        else:
            auth_url = f"{protocol}://:{token_encoded}@{rest}"
    else:
        raise ValueError(f"Invalid URL format: {base_url}")
    
    return auth_url


def sanitize_url_for_logging(url: str) -> str:
    """
    Remove credentials from URL for safe logging.
    
    Args:
        url: URL that may contain credentials
        
    Returns:
        URL with credentials removed
    """
    # Remove any authentication information from URL for logging
    return re.sub(r'://[^@]*@', '://', url)


@blitzy_exponential_retry()
def clone_azure_devops_repository_with_auth(
    organization: str,
    project: str,
    repo_name: str,
    access_token: str,
    clone_path: str,
    branch_name: str = "main",
    commit_hash: Optional[str] = None
) -> bool:
    """
    Clone Azure DevOps repository using git command line with authentication.

    Args:
        organization: Azure DevOps organization name
        project: Project name
        repo_name: Repository name
        access_token: Azure DevOps access token
        clone_path: Local path where to clone the repository
        branch_name: Branch to clone (default: "main")
        commit_hash: Specific commit to checkout after cloning

    Returns:
        True if successful, False otherwise

    Raises:
        AzureDevOpsAuthenticationError: Authentication failed
        AzureDevOpsRepositoryNotFoundError: Repository not found
        AzureDevOpsNetworkError: Network-related errors
    """
    import fcntl

    # Create a lock file path based on the clone path
    lock_file_path = f"{clone_path}.lock"
    lock_file = None

    try:
        # Create parent directories for lock file if they don't exist
        os.makedirs(os.path.dirname(lock_file_path), exist_ok=True)

        # Acquire exclusive lock to prevent concurrent clones to same path
        lock_file = open(lock_file_path, 'w')
        fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX)
        logger.info(f"Acquired lock for cloning to: {clone_path}")

        # Check if directory already exists and is a valid git repository
        if os.path.exists(clone_path):
            git_dir = os.path.join(clone_path, '.git')
            if os.path.exists(git_dir):
                logger.info(f"Directory {clone_path} already contains a git repository, reusing it")

                # Verify it's the correct repository by checking remote URL
                try:
                    result = subprocess.run(
                        ["git", "-C", clone_path, "remote", "get-url", "origin"],
                        capture_output=True, text=True, check=False
                    )
                    if result.returncode == 0:
                        existing_url = result.stdout.strip()
                        # Check if this matches our expected Azure DevOps repository
                        expected_repo_path = f"{organization}/{project}/_git/{repo_name}"
                        if expected_repo_path in existing_url:
                            logger.info(f"Existing repository matches expected Azure DevOps repo {organization}/{project}/{repo_name}, skipping clone")
                            return True
                except Exception as e:
                    logger.warning(f"Could not verify existing repository: {e}")

            logger.info(f"Removing existing directory: {clone_path}")
            shutil.rmtree(clone_path)

        # Create parent directories if they don't exist
        os.makedirs(clone_path, exist_ok=True)

        # Build clone URL with authentication
        base_url = build_azure_devops_clone_url(organization, project, repo_name)
        clone_url = add_auth_to_azure_devops_url(base_url, access_token)
        
        # Log sanitized URL for debugging
        safe_url = sanitize_url_for_logging(clone_url)
        logger.info(f"Cloning Azure DevOps repository: {safe_url} to {clone_path}")

        # Set up environment for git command
        env = os.environ.copy()
        env['GIT_TERMINAL_PROMPT'] = '0'
        env['GIT_ASKPASS'] = 'echo'
        env['GCM_INTERACTIVE'] = 'never'
        env['GIT_CREDENTIAL_HELPER'] = ''

        # Build git clone command
        clone_cmd = [
            "git",
            "-c", "credential.helper=",
            "-c", "core.askpass=",
            "-c", "credential.interactive=never",
            "clone",
            "--recurse-submodules"  # Clone submodules recursively
        ]

        # Add branch specification if no specific commit is requested
        if branch_name and not commit_hash:
            clone_cmd.extend(["-b", branch_name])

        # Add repository URL and destination
        clone_cmd.extend([clone_url, clone_path])

        # Execute clone command
        result = subprocess.run(clone_cmd, capture_output=True, text=True, env=env, check=False)
        
        if result.returncode != 0:
            error_msg = result.stderr.strip()
            safe_error = sanitize_url_for_logging(error_msg)
            logger.error(f"Error cloning Azure DevOps repository: {safe_error}")
            
            # Classify error types for better handling
            if "authentication failed" in error_msg.lower() or "401" in error_msg:
                raise AzureDevOpsAuthenticationError(f"Authentication failed: {safe_error}")
            elif "repository not found" in error_msg.lower() or "404" in error_msg:
                raise AzureDevOpsRepositoryNotFoundError(f"Repository not found: {safe_error}")
            elif "network" in error_msg.lower() or "timeout" in error_msg.lower():
                raise AzureDevOpsNetworkError(f"Network error: {safe_error}")
            else:
                raise AzureDevOpsGitCloneError(f"Git clone failed: {safe_error}")

        logger.info(f"Azure DevOps repository cloned successfully to: {clone_path}")

        # Configure git to not use credential helpers for this repository
        subprocess.run(
            ["git", "-C", clone_path, "config", "--local", "credential.helper", ""],
            capture_output=True,
            check=False
        )

        # Configure submodule URLs with authentication for Azure DevOps
        _configure_azure_devops_submodule_auth(clone_path, access_token, env)

        # If specific commit hash is provided, checkout that commit
        if commit_hash:
            logger.info(f"Checking out specific commit: {commit_hash}")
            checkout_cmd = ["git", "-c", "credential.helper=", "-C", clone_path, "checkout", commit_hash]
            checkout_result = subprocess.run(checkout_cmd, capture_output=True, text=True, env=env, check=False)

            if checkout_result.returncode != 0:
                error_msg = checkout_result.stderr.strip()
                logger.error(f"Error checking out commit {commit_hash}: {error_msg}")
                raise AzureDevOpsGitCloneError(f"Failed to checkout commit {commit_hash}: {error_msg}")

            logger.info(f"Checked out commit: {commit_hash}")

            # Update submodules to match the checked out commit
            logger.info("Updating submodules to match the checked out commit")

            # Re-configure submodule authentication after checkout (in case it was reset)
            logger.info("Re-configuring submodule authentication after commit checkout")
            _configure_azure_devops_submodule_auth(clone_path, access_token, env)

            # Step 1: Initialize submodules first
            logger.info("Step 1: Initializing submodules")
            init_cmd = [
                "git", "-c", "credential.helper=", "-C", clone_path,
                "submodule", "init"
            ]
            init_result = subprocess.run(init_cmd, capture_output=True, text=True, env=env, check=False)

            if init_result.returncode != 0:
                logger.warning(f"Submodule init failed: {init_result.stderr}")
            else:
                logger.info("Submodules initialized successfully")

            # Step 2: Re-configure authentication after init (submodule URLs might be reset)
            logger.info("Step 2: Re-configuring submodule authentication after init")
            _configure_azure_devops_submodule_auth(clone_path, access_token, env)

            # Step 3: Update submodules
            logger.info("Step 3: Updating submodules")
            submodule_update_cmd = [
                "git", "-c", "credential.helper=", "-C", clone_path,
                "submodule", "update", "--recursive"
            ]
            submodule_result = subprocess.run(submodule_update_cmd, capture_output=True, text=True, env=env, check=False)

            if submodule_result.returncode != 0:
                error_msg = submodule_result.stderr.strip()
                logger.warning(f"Warning: Submodule update failed: {error_msg}")
                logger.warning(f"Submodule stderr: {submodule_result.stderr}")
                logger.warning(f"Submodule stdout: {submodule_result.stdout}")

                # Try alternative approach: clone submodules manually
                logger.info("Attempting manual submodule clone as fallback")
                _manually_clone_submodules(clone_path, access_token, env)
            else:
                logger.info("Submodules updated successfully to match the checked out commit")

                # Step 4: Verify submodules were actually cloned (check for case where .gitmodules exists but submodules aren't committed)
                logger.info("Step 4: Verifying submodules were actually cloned")
                gitmodules_path = os.path.join(clone_path, '.gitmodules')
                if os.path.exists(gitmodules_path):
                    # Check if any submodule directories actually exist
                    submodules_exist = False
                    try:
                        config_result = subprocess.run(
                            ["git", "-C", clone_path, "config", "--file", ".gitmodules", "--list"],
                            capture_output=True, text=True, env=env, check=False
                        )

                        if config_result.returncode == 0:
                            for line in config_result.stdout.strip().split('\n'):
                                if '.path=' in line:
                                    path = line.split('=', 1)[1].strip()
                                    submodule_full_path = os.path.join(clone_path, path)
                                    if os.path.exists(submodule_full_path) and os.listdir(submodule_full_path):
                                        submodules_exist = True
                                        logger.info(f"Verified submodule exists: {path}")
                                        break

                        if not submodules_exist:
                            logger.warning("Submodules appear to be defined in .gitmodules but not actually committed/cloned")
                            logger.info("Attempting manual submodule clone for uncommitted submodules")
                            _manually_clone_submodules(clone_path, access_token, env)

                    except Exception as e:
                        logger.warning(f"Failed to verify submodules: {e}")

        return True

    except (AzureDevOpsAuthenticationError, AzureDevOpsRepositoryNotFoundError,
            AzureDevOpsNetworkError, AzureDevOpsGitCloneError):
        # Re-raise our custom exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during Azure DevOps repository clone: {str(e)}")
        raise AzureDevOpsGitCloneError(f"Unexpected error: {str(e)}")

    finally:
        # Release lock and clean up lock file
        if lock_file:
            try:
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                lock_file.close()
                if os.path.exists(lock_file_path):
                    os.remove(lock_file_path)
                logger.info(f"Released lock for: {clone_path}")
            except Exception as e:
                logger.warning(f"Error releasing lock: {e}")


def _configure_azure_devops_submodule_auth(clone_path: str, access_token: str, env: dict):
    """
    Configure submodule URLs with authentication for Azure DevOps.

    Args:
        clone_path: Path to the cloned repository
        access_token: Azure DevOps access token
        env: Environment variables for git commands
    """
    try:
        # Check if there are any submodules
        gitmodules_path = os.path.join(clone_path, '.gitmodules')
        if not os.path.exists(gitmodules_path):
            logger.debug("No .gitmodules file found, skipping submodule auth configuration")
            return

        logger.info("Found .gitmodules file, configuring submodule authentication")

        # Parse .gitmodules file directly to get submodule information
        submodules = []
        try:
            with open(gitmodules_path, 'r') as f:
                content = f.read()
                logger.debug(f".gitmodules content: {content}")

            # Parse .gitmodules using git config
            config_result = subprocess.run(
                ["git", "-C", clone_path, "config", "--file", ".gitmodules", "--list"],
                capture_output=True,
                text=True,
                env=env,
                check=False
            )

            if config_result.returncode == 0:
                submodule_paths = set()
                for line in config_result.stdout.strip().split('\n'):
                    if line.strip() and 'submodule.' in line and '.path=' in line:
                        # Extract submodule path from line like "submodule.libs/mysubmodule.path=libs/mysubmodule"
                        parts = line.split('=', 1)
                        if len(parts) == 2:
                            submodule_path = parts[1].strip()
                            submodule_paths.add(submodule_path)
                            logger.info(f"Found submodule path: {submodule_path}")

                submodules = list(submodule_paths)
            else:
                logger.warning(f"Failed to parse .gitmodules: {config_result.stderr}")
                return

        except Exception as e:
            logger.warning(f"Failed to read .gitmodules file: {e}")
            return

        if not submodules:
            logger.debug("No submodules found in .gitmodules")
            return

        logger.info(f"Configuring authentication for {len(submodules)} submodules: {submodules}")

        # Configure authentication for each Azure DevOps submodule
        for submodule_path in submodules:
            try:
                # Get the submodule name from the path (for git config)
                # Need to find the submodule name in .gitmodules
                submodule_name = None
                config_list_result = subprocess.run(
                    ["git", "-C", clone_path, "config", "--file", ".gitmodules", "--list"],
                    capture_output=True,
                    text=True,
                    env=env,
                    check=False
                )

                if config_list_result.returncode == 0:
                    for line in config_list_result.stdout.strip().split('\n'):
                        if f'.path={submodule_path}' in line:
                            # Extract submodule name from line like "submodule.libs/mysubmodule.path=libs/mysubmodule"
                            if line.startswith('submodule.') and '.path=' in line:
                                submodule_name = line.split('.path=')[0].replace('submodule.', '')
                                break

                if not submodule_name:
                    logger.warning(f"Could not find submodule name for path: {submodule_path}")
                    continue

                logger.info(f"Configuring auth for submodule: {submodule_name} (path: {submodule_path})")

                # Get the submodule URL
                url_result = subprocess.run(
                    ["git", "-C", clone_path, "config", "--file", ".gitmodules",
                     f"submodule.{submodule_name}.url"],
                    capture_output=True,
                    text=True,
                    env=env,
                    check=False
                )

                if url_result.returncode != 0:
                    logger.warning(f"Could not get URL for submodule {submodule_name}")
                    continue

                submodule_url = url_result.stdout.strip()
                logger.info(f"Submodule {submodule_name} URL: {submodule_url}")

                # Check if it's an Azure DevOps URL
                if _is_azure_devops_url(submodule_url):
                    # Add authentication to the URL
                    auth_url = add_auth_to_azure_devops_url(submodule_url, access_token)
                    logger.info(f"Setting authenticated URL for submodule {submodule_name}")

                    # Update the submodule URL with authentication in git config
                    subprocess.run(
                        ["git", "-C", clone_path, "config",
                         f"submodule.{submodule_name}.url", auth_url],
                        capture_output=True,
                        env=env,
                        check=False
                    )

                    logger.debug(f"Configured auth for Azure DevOps submodule: {submodule_path}")

            except Exception as e:
                logger.warning(f"Failed to configure auth for submodule {submodule_path}: {e}")

    except Exception as e:
        logger.warning(f"Failed to configure submodule authentication: {e}")


def _manually_clone_submodules(clone_path: str, access_token: str, env: dict):
    """
    Manually clone submodules as a fallback when git submodule update fails.

    Args:
        clone_path: Path to the cloned repository
        access_token: Azure DevOps access token
        env: Environment variables for git commands
    """
    try:
        gitmodules_path = os.path.join(clone_path, '.gitmodules')
        if not os.path.exists(gitmodules_path):
            logger.debug("No .gitmodules file found for manual clone")
            return

        logger.info("Attempting manual submodule cloning")

        # Parse .gitmodules to get submodule info
        config_result = subprocess.run(
            ["git", "-C", clone_path, "config", "--file", ".gitmodules", "--list"],
            capture_output=True,
            text=True,
            env=env,
            check=False
        )

        if config_result.returncode != 0:
            logger.warning("Failed to read .gitmodules for manual clone")
            return

        submodules = {}  # name -> {path, url}
        for line in config_result.stdout.strip().split('\n'):
            if line.strip() and 'submodule.' in line:
                if '.path=' in line:
                    parts = line.split('.path=', 1)
                    if len(parts) == 2:
                        name = parts[0].replace('submodule.', '')
                        path = parts[1].strip()
                        if name not in submodules:
                            submodules[name] = {}
                        submodules[name]['path'] = path
                elif '.url=' in line:
                    parts = line.split('.url=', 1)
                    if len(parts) == 2:
                        name = parts[0].replace('submodule.', '')
                        url = parts[1].strip()
                        if name not in submodules:
                            submodules[name] = {}
                        submodules[name]['url'] = url

        # Clone each submodule manually
        for name, info in submodules.items():
            if 'path' not in info or 'url' not in info:
                logger.warning(f"Incomplete submodule info for {name}: {info}")
                continue

            submodule_path = info['path']
            submodule_url = info['url']
            full_submodule_path = os.path.join(clone_path, submodule_path)

            logger.info(f"Manually cloning submodule {name} to {submodule_path}")

            # Create parent directory if needed
            os.makedirs(os.path.dirname(full_submodule_path), exist_ok=True)

            # Add authentication to Azure DevOps URLs
            if _is_azure_devops_url(submodule_url):
                submodule_url = add_auth_to_azure_devops_url(submodule_url, access_token)

            # Clone the submodule
            clone_cmd = [
                "git", "-c", "credential.helper=", "-c", "core.askpass=",
                "-c", "credential.interactive=never", "clone", submodule_url, full_submodule_path
            ]

            clone_result = subprocess.run(clone_cmd, capture_output=True, text=True, env=env, check=False)

            if clone_result.returncode == 0:
                logger.info(f"Successfully cloned submodule {name}")
            else:
                logger.warning(f"Failed to manually clone submodule {name}: {clone_result.stderr}")

    except Exception as e:
        logger.warning(f"Failed to manually clone submodules: {e}")


def _is_azure_devops_url(url: str) -> bool:
    """
    Check if a URL is an Azure DevOps URL.

    Args:
        url: Git URL to check

    Returns:
        True if it's an Azure DevOps URL, False otherwise
    """
    if not url:
        return False

    azure_patterns = [
        'dev.azure.com',
        'visualstudio.com',
        'ssh.dev.azure.com'
    ]

    return any(pattern in url.lower() for pattern in azure_patterns)


def get_azure_devops_git_clone_config() -> dict:
    """
    Get Azure DevOps git clone configuration from environment variables.

    Returns:
        Configuration dictionary with git clone settings
    """
    return {
        'use_git_clone': os.getenv('AZURE_DEVOPS_USE_GIT_CLONE', 'true').lower() == 'true',
        'fallback_to_api': os.getenv('AZURE_DEVOPS_FALLBACK_TO_API', 'true').lower() == 'true',
        'clone_timeout': int(os.getenv('AZURE_DEVOPS_GIT_CLONE_TIMEOUT', '900')),
        'cleanup_after_processing': os.getenv('AZURE_DEVOPS_CLEANUP_CLONED_REPOS', 'true').lower() == 'true'
    }
